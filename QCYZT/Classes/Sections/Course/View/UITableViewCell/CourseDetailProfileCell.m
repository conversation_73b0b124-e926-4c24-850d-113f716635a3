//
//  CourseDetailProfileCell.m
//  QCYZT
//
//  Created by zeng on 2022/9/27.
//  Copyright © 2022 LZKJ. All rights reserved.
//

#import "CourseDetailProfileCell.h"
#import "CourseDetailModel.h"
#import "SDCycleScrollView.h"
#import "HZPhotoBrowser.h"
#import "SDCollectionViewCell.h"
@interface CourseDetailProfileCell()<HZPhotoBrowserDelegate,UIScrollViewDelegate>

@property (nonatomic, strong) UILabel *titleLabel;

@property (nonatomic, strong) UIView *grayView;
@property (nonatomic, strong) UIView *dakaView;
@property (nonatomic, strong) UIImageView *headImV;
@property (nonatomic, strong) UIImageView *authenticationImgV;
@property (nonatomic, strong) UILabel *nameLB;
@property (nonatomic, strong) ZLTagView *tagView;
/// 关注数
@property (nonatomic, strong) UILabel *focusNumLB;
/// 关注按钮
@property (nonatomic, strong) FocusButton *focusBtn;
/// 投顾简介
@property (nonatomic, strong) UILabel *authorProfileLabel;
/// 课程简介
@property (nonatomic, strong) UILabel *courseProfileLabel;
/// 已读数
@property (nonatomic, strong) UILabel *readNumLabel;
/// 时间
@property (nonatomic, strong) UILabel *timeLabel;
/// ppt视图
@property (nonatomic, strong) UIView *pptView;
/// 免责声明
@property (nonatomic, strong) UILabel *descLabel;

@property (nonatomic, strong) UIStackView *imageStackView;

@property (nonatomic, strong) UIScrollView *imageScrollView;

@property (nonatomic, strong) ZLTagLabel *indexLB;

@property (nonatomic, strong) UILabel *pptTitle;

@property (nonatomic,assign) NSInteger currentIndex;

@end

@implementation CourseDetailProfileCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self addSubviews];
    }
    return self;
}

- (void)addSubviews {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.contentView.backgroundColor = UIColor.up_contentBgColor;
    
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.top.equalTo(@12);
    }];
    
    UIView *grayView = [[UIView alloc] init];
    [self.contentView addSubview:grayView];
    [grayView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.titleLabel);
        make.top.equalTo(self.titleLabel.mas_bottom).offset(12);
    }];
    grayView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
    UI_View_Radius(grayView, 5);
    self.grayView = grayView;
    
    [grayView addSubview:self.dakaView];
    [self.dakaView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(@0);
        make.top.equalTo(@12);
    }];
    
    [grayView addSubview:self.authorProfileLabel];
    [self.authorProfileLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.dakaView.mas_bottom).offset(8);
        make.left.equalTo(@15);
        make.right.equalTo(@-15);
        make.bottom.equalTo(@-15);
    }];
    
    [self.contentView addSubview:self.courseProfileLabel];
    [self.courseProfileLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(grayView);
        make.top.equalTo(grayView.mas_bottom).offset(15);
    }];
    
    [self.contentView addSubview:self.pptView];
    [self.pptView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.courseProfileLabel);
        make.top.equalTo(self.courseProfileLabel.mas_bottom).offset(15);
        make.height.equalTo(CGFLOAT_MIN);
    }];
    
    [self.contentView addSubview:self.descLabel];
    [self.descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.courseProfileLabel);
        make.top.equalTo(self.pptView.mas_bottom).offset(15);
    }];
    
    UIView *line = [[UIView alloc] init];
    line.backgroundColor = UIColor.fm_sepline_color;
    [self.contentView addSubview:line];
    [line mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(self.descLabel);
        make.top.equalTo(self.descLabel.mas_bottom).offset(20);
        make.height.equalTo(0.7);
        make.bottom.equalTo(-56);
    }];
    
    UILabel *readNumLB = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:readNumLB];
    [readNumLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(line.mas_bottom).offset(20);
        make.left.equalTo(self.descLabel);
    }];
    self.readNumLabel = readNumLB;
    
    UILabel *timeLB = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:1 textAlignment:NSTextAlignmentLeft];
    [self.contentView addSubview:timeLB];
    [timeLB mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(readNumLB);
        make.right.equalTo(self.descLabel);
    }];
    self.timeLabel = timeLB;
}

// 上一页
- (void)previousBtnClick {
    if (self.currentIndex > 0) {
        self.currentIndex -= 1;
        [self.imageScrollView setContentOffset:CGPointMake((UI_SCREEN_WIDTH - 30) * self.currentIndex, 0) animated:YES];
        self.indexLB.text = [NSString stringWithFormat:@"%ld/%ld",self.currentIndex + 1,self.model.pptImgs.count];

    }
}

// 下一页
- (void)nextBtnClick {
    if (self.currentIndex < self.model.pptImgs.count) {
        self.currentIndex += 1;
        [self.imageScrollView setContentOffset:CGPointMake((UI_SCREEN_WIDTH - 30) * self.currentIndex, 0) animated:YES];
        self.indexLB.text = [NSString stringWithFormat:@"%ld/%ld",self.currentIndex + 1,self.model.pptImgs.count];

    }
}

#pragma mark - SDCycleScrollView代理方法
- (void)scrollViewDidEndDecelerating:(UIScrollView *)scrollView {
    NSInteger index = scrollView.contentOffset.x / (UI_SCREEN_WIDTH - 30);
    self.indexLB.text = [NSString stringWithFormat:@"%ld/%ld",index + 1,self.model.pptImgs.count];
    self.currentIndex = index;
    
    UIImageView *imageView = self.imageStackView.arrangedSubviews[index];
    [imageView sd_setImageWithURL:[NSURL URLWithString:self.model.pptImgs[index]] placeholderImage:ImageWithName(@"sphc_placeholder")];
}

#pragma mark - photobrowser代理方法
- (UIImage *)photoBrowser:(HZPhotoBrowser *)browser placeholderImageForIndex:(NSInteger)index {
    UIImageView *imageV = (UIImageView *)self.imageStackView.arrangedSubviews[index];
    return imageV.image;
}

- (NSURL *)photoBrowser:(HZPhotoBrowser *)browser highQualityImageURLForIndex:(NSInteger)index {
    return [NSURL URLWithString:self.model.pptImgs[index]];
}

- (void)setModel:(CourseDetailModel *)model {
    _model = model;
    
    if (model.courseTitle.length) {
        self.titleLabel.text = model.courseTitle;
    } else {
        self.titleLabel.text = @"";
    }
        
    [self.headImV sd_setImageWithURL:[NSURL URLWithString:model.bignameDto.userIco] placeholderImage:ImageWithName(@"userCenter_dltx")];
    self.authenticationImgV.hidden = (model.bignameDto.attestationType == 0);
    if (model.bignameDto.attestationType == 1) {
        self.authenticationImgV.image = ImageWithName(@"MemberCenter_AttestationTypeColumn");
    } else if (model.bignameDto.attestationType == 2) {
        self.authenticationImgV.image = ImageWithName(@"MemberCenter_AttestationTypeConsultant");
    } else {
        self.authenticationImgV.image = ImageWithName(@"MemberCenter_AttestationTypeOrganization");
    }
    if (model.bignameDto.userName.length) {
        self.nameLB.text = model.bignameDto.userName;
        // 手动计算，自动约束与tagLb有冲突
        CGSize size = [model.bignameDto.userName sizeWithFont:[FMHelper scaleBoldFont:16] andSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX)];
        [self.nameLB mas_updateConstraints:^(MASConstraintMaker *make) {
            make.width.equalTo(@(ceil(size.width)));
        }];
    } else {
        self.nameLB.text = @"";
    }
    
    if (model.bignameDto.userGoodAt.length) {
        NSArray *goodAts = [model.bignameDto.userGoodAt componentsSeparatedByString:@"、"];
        self.tagView.titleArray = goodAts;
        self.tagView.hidden = NO;
    } else {
        self.tagView.hidden = YES;
    }
    
    if (model.bignameDto.userNoticerNums > 0) {
        NSString *focusNumStr = nil;
        if (self.model.bignameDto.userNoticerNums < 10000) {
            focusNumStr = [NSString stringWithFormat:@"%ld人关注", self.model.bignameDto.userNoticerNums];
        } else {
            double wan = self.model.bignameDto.userNoticerNums / 10000.0;
            focusNumStr = [NSString stringWithFormat:@"%.1f万人关注", wan];
        }
        self.focusNumLB.text = focusNumStr;
    }  else {
        self.focusNumLB.text = @"0人关注";
    }
    
    self.focusBtn.dakaId = model.bignameDto.userId;
    self.focusBtn.isFocus = [[FMUserDataSyncManager sharedManager] isDakaNoticed:model.bignameDto.userId];
    
    self.authorProfileLabel.text = model.bignameDto.userProfiles;
    
    if (model.courseSummary.length > 0) {
        NSMutableParagraphStyle *style = [[NSMutableParagraphStyle alloc] init];
        style.lineSpacing = 3;
        NSDictionary *dic = @{NSParagraphStyleAttributeName:style};
        NSMutableAttributedString *attrString = [[NSMutableAttributedString alloc] initWithString:model.courseSummary attributes:dic];
        self.courseProfileLabel.attributedText = attrString;
        [self.courseProfileLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.grayView.mas_bottom).offset(15);
        }];
    } else {
        self.courseProfileLabel.text = @"";
        [self.courseProfileLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.grayView.mas_bottom).offset(0);
        }];
    }
    
    if (model.declareContent.length) {
        self.descLabel.text = model.declareContent;
        [self.descLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.courseProfileLabel.mas_bottom).offset(20);
        }];
    } else {
        self.descLabel.text = @"";
        [self.descLabel mas_updateConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(self.courseProfileLabel.mas_bottom).offset(0);
        }];
    }
    
    NSString *readNumStr = @"";
    if (model.readerNums.integerValue < 10000) {
        readNumStr = [NSString stringWithFormat:@"%@人看过", model.readerNums];
    } else {
        double wan = model.readerNums.integerValue / 10000.0;
        readNumStr = [NSString stringWithFormat:@"%.1f万人看过", wan];
    }
    self.readNumLabel.text = readNumStr;
    
    NSDate *nowDate = [NSDate dateWithTimeIntervalSince1970:model.courseTime / 1000];
    
    NSString *timeStr = nil;
    if ([nowDate isToday]) {
        timeStr = [NSString stringFromDate:nowDate format:@"HH:mm"];
    } else {
        if ([nowDate isThisYear]) {
            timeStr = [NSString stringFromDate:nowDate format:@"MM-dd HH:mm"];
        } else {
            timeStr = [NSString stringFromDate:nowDate format:@"yyyy-MM-dd"];
        }
    }
    self.timeLabel.text = timeStr;
    
    // PPT显示逻辑：没有PPT图片时隐藏，有权限或可以试看时显示
    BOOL hasAuthority = (model.authority.integerValue == 1);
    BOOL canTryPlay = (model.isTryPlay.integerValue > 0);
    BOOL shouldHidePPT = (model.pptImgs.count == 0) || (!hasAuthority && !canTryPlay);
    self.pptView.hidden = shouldHidePPT;
    [self.pptView mas_updateConstraints:^(MASConstraintMaker *make) {
        if (shouldHidePPT) {
            make.height.equalTo(CGFLOAT_MIN);
            make.top.equalTo(self.courseProfileLabel.mas_bottom).offset(0);
        } else {
            make.height.equalTo((UI_SCREEN_WIDTH - 30) * 9 / 16.0 + 38);
            make.top.equalTo(self.courseProfileLabel.mas_bottom).offset(15);
        }
    }];
    
    for (UIImageView *img in self.imageStackView.arrangedSubviews) {
        [img removeFromSuperview];
    }

    // 只有在有权限且有PPT图片时才创建PPT图片视图
    if (!shouldHidePPT) {
        for (NSInteger i = 0; i < model.pptImgs.count; i ++) {
            UIImageView *imageV = [[UIImageView alloc] init];
            imageV.contentMode = UIViewContentModeScaleAspectFill;
            imageV.clipsToBounds = YES;
            imageV.tag = i;
            imageV.userInteractionEnabled = YES;
            [self.imageStackView addArrangedSubview:imageV];
            [imageV mas_makeConstraints:^(MASConstraintMaker *make) {
                make.size.equalTo(CGSizeMake(UI_SCREEN_WIDTH - 30, 210));
            }];
            // 这里只加载第一张图片,其余图片在滑动到展示区的时候再加载  一次性加载太多图片 会瞬时占用很多内存 低版本系统也可能发生闪退
            if (i == 0) {
                [imageV sd_setImageWithURL:[NSURL URLWithString:model.pptImgs[i]] placeholderImage:ImageWithName(@"sphc_placeholder")];
            }
            UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(UITapGestureRecognizer *sender) {
                HZPhotoBrowser *browserVc = [[HZPhotoBrowser alloc] init];
                browserVc.sourceImagesContainerView = self.imageStackView; // 原图的父控件
                browserVc.imageCount = self.model.pptImgs.count; // 图片总数
                browserVc.currentImageIndex = (int)sender.view.tag;
                browserVc.delegate = self;
                [browserVc show];
            }];
            [imageV addGestureRecognizer:tap];
        }
        self.indexLB.text = [NSString stringWithFormat:@"1/%ld",self.model.pptImgs.count];
    }

    
}

- (UILabel *)titleLabel {
    if (_titleLabel == nil) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.font = [FMHelper scaleBoldFont:20.0];
        _titleLabel.textColor = UIColor.up_textPrimaryColor;
        _titleLabel.numberOfLines = 0;
    }
    return _titleLabel;
}

- (UIView *)dakaView {
    if (!_dakaView) {
        _dakaView = [UIView new];
        
        // 头像
        UIImageView *headImgV = [[UIImageView alloc] init];
        [_dakaView addSubview:headImgV];
        [headImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(@15);
            make.centerY.equalTo(@0);
            make.width.height.equalTo(@42);
        }];
        UI_View_Radius(headImgV, 21);
        headImgV.contentMode = UIViewContentModeScaleAspectFill;
        headImgV.userInteractionEnabled = YES;
        WEAKSELF
        [headImgV bk_whenTapped:^{
            [ProtocolJump jumpWithUrl:[NSString stringWithFormat:@"qcyzt://bigname?id=%@", __weakSelf.model.bignameDto.userId]];
        }];
        self.headImV = headImgV;
        
        // 认证图标
        UIImageView *authenticationImgV = [[UIImageView alloc] init];
        [_dakaView addSubview:authenticationImgV];
        [authenticationImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(headImgV.mas_right).offset(2);
            make.bottom.equalTo(headImgV.mas_bottom).offset(2);
            make.width.height.equalTo(@16);
        }];
        self.authenticationImgV = authenticationImgV;
        
        // 名称
        UILabel *nameLB = [[UILabel alloc] init];
        [_dakaView addSubview:nameLB];
        [nameLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(@2);
            make.left.equalTo(headImgV.mas_right).offset(8);
            make.width.equalTo(@0);
        }];
        nameLB.font = [FMHelper scaleBoldFont:16];
        nameLB.textColor = UIColor.up_textPrimaryColor;
        nameLB.backgroundColor = [UIColor clearColor];
        self.nameLB = nameLB;

        // 头衔
        ZLTagView *tagView = [[ZLTagView alloc] init];
        [_dakaView addSubview:tagView];
        [tagView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(nameLB.mas_right).offset(6);
            make.centerY.equalTo(nameLB);
            make.right.equalTo(-100);
            make.height.equalTo(18);
        }];
        tagView.tagLabelFont = [FMHelper scaleFont:12];
        tagView.tagLabelWidthPadding = 10;
        tagView.tagLabelTextColor = ColorWithHex(0xa76d00);
        tagView.tagLabelBgColor = ColorWithHex(0xfff6e2);
        tagView.tagLabelWidthPadding = 5;
        tagView.tagLabelHeightPadding = 3;
        tagView.tagLabelCornerRadius = 2;
        tagView.middlePadding = 5;
        tagView.numberofLines = 1;
        self.tagView = tagView;
        
        // 关注
        UILabel *focusNumLB = [[UILabel alloc] init];
        [_dakaView addSubview:focusNumLB];
        [focusNumLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(nameLB.mas_bottom).offset(3);
            make.left.equalTo(nameLB.mas_left);
            make.bottom.equalTo(@-2);
        }];
        focusNumLB.font = [FMHelper scaleFont:12];
        focusNumLB.textColor = UIColor.up_textSecondary1Color;
        focusNumLB.backgroundColor = [UIColor clearColor];
        self.focusNumLB = focusNumLB;
        
        FocusButton *focusBtn = [FocusButton buttonWithType:UIButtonTypeCustom];
        focusBtn.text = @"关注";
        focusBtn.focusText = @"已关注";
        focusBtn.textColor = FMNavColor;
        focusBtn.focusTextColor = UIColor.fm_stock_calendar_textDisabledColor;
        focusBtn.image = [UIImage imageWithTintColor:FMNavColor blendMode:kCGBlendModeDestinationIn WithImageObject:ImageWithName(@"add_focus")];
        focusBtn.focusImage = ImageWithName(@"");
        focusBtn.boardColor = FMNavColor;
        focusBtn.focusBoardColor = UIColor.fm_stock_calendar_textDisabledColor;
        focusBtn.backColor = UIColor.up_contentBgColor;
        focusBtn.focusBackColor = UIColor.up_contentBgColor;
        focusBtn.titleLabel.font = [FMHelper scaleFont:15];
        focusBtn.compeletionClock = ^(NSString * _Nonnull dakaId, BOOL isfocus) {
            if (__weakSelf.model) {
                if ([dakaId isEqualToString:__weakSelf.model.bignameDto.userId]) {
                    if (isfocus) {
                        __weakSelf.model.bignameDto.userNoticerNums ++;
                    } else {
                        __weakSelf.model.bignameDto.userNoticerNums --;
                    }
                    __weakSelf.model = __weakSelf.model;
                }
            }
        };
        [_dakaView addSubview:focusBtn];
        [focusBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(@0);
            make.right.equalTo(@(-15));
            make.size.equalTo(@(CGSizeMake(75, 30)));
        }];
        self.focusBtn = focusBtn;
    }
    
    return _dakaView;
}

- (UILabel *)authorProfileLabel {
    if (!_authorProfileLabel) {
        _authorProfileLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:14] textColor:UIColor.up_textSecondaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    }
    
    return _authorProfileLabel;
}

- (UILabel *)courseProfileLabel {
    if (!_courseProfileLabel) {
        _courseProfileLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:15] textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    }
    
    return _courseProfileLabel;
}

- (UILabel *)descLabel {
    if (!_descLabel) {
        _descLabel = [[UILabel alloc] initWithFrame:CGRectZero font:[FMHelper scaleFont:12] textColor:UIColor.up_textSecondary1Color backgroundColor:FMClearColor numberOfLines:0 textAlignment:NSTextAlignmentLeft];
    }
    
    return _descLabel;
}

- (UIView *)pptView {
    if (!_pptView) {
        _pptView = [[UIView alloc] init];
        UIView *topView = [[UIView alloc] init];
        topView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        [_pptView addSubview:topView];
        [topView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.right.equalTo(0);
            make.height.equalTo(38);
        }];
        
        UILabel *pptTitle = [[UILabel alloc] initWithFrame:CGRectZero font:FontWithSize(15) textColor:UIColor.up_textPrimaryColor backgroundColor:FMClearColor numberOfLines:1];
        [topView addSubview:pptTitle];
        [pptTitle mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.centerY.equalTo(0);
        }];
        pptTitle.text = [NSString stringWithFormat:@"本节PPT课程"];
        self.pptTitle = pptTitle;
        
        UIButton *previousBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [previousBtn setImage:ImageWithName(@"previous") forState:UIControlStateNormal];
        [previousBtn addTarget:self action:@selector(previousBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [topView addSubview:previousBtn];
        [previousBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.bottom.equalTo(0);
            make.size.equalTo(CGSizeMake(38, 38));
        }];
        
        UIButton *nextBtn = [UIButton buttonWithType:UIButtonTypeCustom];
        [nextBtn setImage:ImageWithName(@"next") forState:UIControlStateNormal];
        [nextBtn addTarget:self action:@selector(nextBtnClick) forControlEvents:UIControlEventTouchUpInside];
        [topView addSubview:nextBtn];
        [nextBtn mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.right.bottom.equalTo(0);
            make.size.equalTo(CGSizeMake(38, 38));
        }];
        
        UIScrollView *imageScrollView = [[UIScrollView alloc] init];
        imageScrollView.showsVerticalScrollIndicator = NO;
        imageScrollView.showsHorizontalScrollIndicator = NO;
        imageScrollView.pagingEnabled = YES;
        imageScrollView.delegate = self;
        [_pptView addSubview:imageScrollView];
        [imageScrollView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(38);
            make.left.right.bottom.equalTo(0);
            make.height.equalTo((UI_SCREEN_WIDTH - 30) * 9 / 16.0);
        }];
        self.imageScrollView = imageScrollView;
        
        UIStackView *imageStackView = [[UIStackView alloc] init];
        imageStackView.axis = UILayoutConstraintAxisHorizontal;
        [imageScrollView addSubview:imageStackView];
        [imageStackView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.insets(UIEdgeInsetsZero);
            make.height.equalTo((UI_SCREEN_WIDTH - 30) * 9 / 16.0);
        }];
        self.imageStackView = imageStackView;
        
        
        ZLTagLabel *indexLB = [[ZLTagLabel alloc] initWithFrame:CGRectZero font:FontWithSize(12) textColor:FMWhiteColor backgroundColor:ColorWithHexAlpha(0x000000, 0.6) numberOfLines:1 textAlignment:NSTextAlignmentCenter];
        indexLB.widthPadding = 10;
        indexLB.heightPadding = 4;
        [_pptView addSubview:indexLB];
        [indexLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(10);
            make.bottom.equalTo(-10);
        }];
        UI_View_Radius(indexLB, 4);
        self.indexLB = indexLB;
        
        self.currentIndex = 0;
        
    }
    return _pptView;
}


@end
