//
//  FMGSYJChiefInvestmentHoldsCell.m
//  QCYZT
//
//  Created by Augment on 2024/6/28.
//  Copyright © 2024 LZKJ. All rights reserved.
//

#import "FMGSYJChiefInvestmentHoldsCell.h"
#import "FMGSYJChiefInvestmentHoldsOperationCell.h"

@interface FMGSYJChiefInvestmentHoldsCell()<UITableViewDelegate, UITableViewDataSource>

@property (nonatomic, strong) UILabel *stockNameLabel;
@property (nonatomic, strong) UILabel *stockCodeLabel;
@property (nonatomic, strong) UILabel *currentPriceLabel;
@property (nonatomic, strong) UILabel *costPriceLabel;
@property (nonatomic, strong) UILabel *highLowPriceLabel;
@property (nonatomic, strong) UILabel *positionRatioLabel;
@property (nonatomic, strong) UIButton *foldButton;
@property (nonatomic, strong) UILabel *currentPriceTitleLabel;
@property (nonatomic, strong) UILabel *highLowPriceTitleLabel;
@property (nonatomic, strong) UILabel *positionRatioTitleLabel;
@property (nonatomic, strong) UITableView *detailTableView;
@property (nonatomic, strong) UIStackView *mainStackView;
@property (nonatomic, strong) UIView *styleView;
@property (nonatomic, strong) UILabel *styleValueLabel;

@end

@implementation FMGSYJChiefInvestmentHoldsCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupUI];
    }
    return self;
}

- (void)setupUI {
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    self.contentView.backgroundColor = UIColor.up_contentBgColor;

    // 主StackView
    self.mainStackView = [[UIStackView alloc] initWithArrangedSubviews:@[]];
    self.mainStackView.axis = UILayoutConstraintAxisVertical;
    self.mainStackView.alignment = UIStackViewAlignmentCenter;
    self.mainStackView.distribution = UIStackViewDistributionFill;
    self.mainStackView.spacing = 0;
    [self.contentView addSubview:self.mainStackView];
    [self.mainStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.contentView);
    }];

    // 顶部信息视图
    UIView *topView = [[UIView alloc] init];
    {
        [self.mainStackView addArrangedSubview:topView];
        [topView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(0);
        }];
        topView.userInteractionEnabled = YES;
        [topView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(foldButtonClicked)]];
        
        // 股票名称
        UIView *nameCodeView = [UIView new];
        [topView addSubview:nameCodeView];
        [nameCodeView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(15);
            make.centerY.equalTo(0);
            make.top.equalTo(15);
        }];
        nameCodeView.userInteractionEnabled = YES;
        [nameCodeView addGestureRecognizer:[[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(jumpToStockDetail)]];
        
        self.stockNameLabel = [[UILabel alloc] init];
        self.stockNameLabel.font = BoldFontWithSize(14);
        self.stockNameLabel.textColor = UIColor.fm_market_nav_text_zeroColor;
        [nameCodeView addSubview:self.stockNameLabel];
        [self.stockNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.top.equalTo(0);
            make.right.lessThanOrEqualTo(0);
        }];
        
        // 股票代码
        self.stockCodeLabel = [[UILabel alloc] init];
        self.stockCodeLabel.font = FontWithSize(12);
        self.stockCodeLabel.textColor = UIColor.up_textPrimaryColor;
        [nameCodeView addSubview:self.stockCodeLabel];
        [self.stockCodeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(0);
            make.top.equalTo(self.stockNameLabel.mas_bottom).offset(5);
            make.bottom.equalTo(0);
            make.right.lessThanOrEqualTo(0);
        }];
        
        // 折叠按钮
        self.foldButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [self.foldButton setImage:ImageWithName(@"MemberCenter_FoldArrow") forState:UIControlStateNormal];
        self.foldButton.userInteractionEnabled = NO;
        [self.contentView addSubview:self.foldButton];
        self.foldButton.lz_touchAreaInsets = UIEdgeInsetsMake(-10, -10, -10, -10);
        [self.foldButton mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(topView).offset(-15);
            make.centerY.equalTo(topView);
        }];
        
        // 价格标题
        self.currentPriceTitleLabel = [[UILabel alloc] init];
        self.currentPriceTitleLabel.font = FontWithSize(12);
        self.currentPriceTitleLabel.textColor = UIColor.up_textSecondary1Color;
        self.currentPriceTitleLabel.text = @"现价/成本";
        self.currentPriceTitleLabel.textAlignment = NSTextAlignmentCenter;
        [topView addSubview:self.currentPriceTitleLabel];
        
        // 价格值
        self.currentPriceLabel = [[UILabel alloc] init];
        self.currentPriceLabel.font = FontWithSize(14);
        self.currentPriceLabel.textColor = UIColor.fm_market_nav_text_zeroColor;
        self.currentPriceLabel.textAlignment = NSTextAlignmentCenter;
        [topView addSubview:self.currentPriceLabel];
        CGFloat columnWidth = (UI_SCREEN_WIDTH - 30 - 100) / 3.0; // 减去左右边距和股票信息宽度
        [self.currentPriceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(UI_Relative_WidthValue(96.5));
            make.centerY.equalTo(self.stockNameLabel);
            make.width.equalTo(columnWidth);
        }];
        [self.currentPriceTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.currentPriceLabel);
            make.centerY.equalTo(self.stockCodeLabel);
            make.width.equalTo(columnWidth);
        }];
        
        // 最高最低价标题
        self.highLowPriceTitleLabel = [[UILabel alloc] init];
        self.highLowPriceTitleLabel.font = FontWithSize(12);
        self.highLowPriceTitleLabel.textColor = UIColor.up_textSecondary1Color;
        self.highLowPriceTitleLabel.text = @"最高价/最低价";
        self.highLowPriceTitleLabel.textAlignment = NSTextAlignmentCenter;
        [topView addSubview:self.highLowPriceTitleLabel];
        
        
        // 最高最低价值
        self.highLowPriceLabel = [[UILabel alloc] init];
        self.highLowPriceLabel.font = FontWithSize(14);
        self.highLowPriceLabel.textColor = UIColor.fm_market_nav_text_zeroColor;
        self.highLowPriceLabel.textAlignment = NSTextAlignmentCenter;
        [topView addSubview:self.highLowPriceLabel];
        [self.highLowPriceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.currentPriceLabel.mas_right);
            make.centerY.width.equalTo(self.currentPriceLabel);
        }];
        
        [self.highLowPriceTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.highLowPriceLabel);
            make.centerY.equalTo(self.stockCodeLabel);
        }];
        
        // 持仓比例标题
        self.positionRatioTitleLabel = [[UILabel alloc] init];
        self.positionRatioTitleLabel.font = FontWithSize(12);
        self.positionRatioTitleLabel.textColor = UIColor.up_textSecondary1Color;
        self.positionRatioTitleLabel.text = @"持仓比例";
        self.positionRatioTitleLabel.textAlignment = NSTextAlignmentCenter;
        [topView addSubview:self.positionRatioTitleLabel];
        
        // 持仓比例值
        self.positionRatioLabel = [[UILabel alloc] init];
        self.positionRatioLabel.font = FontWithSize(14);
        self.positionRatioLabel.textColor = UIColor.fm_market_nav_text_zeroColor;
        self.positionRatioLabel.textAlignment = NSTextAlignmentCenter;
        [topView addSubview:self.positionRatioLabel];
        [self.positionRatioLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.highLowPriceLabel.mas_right);
            make.centerY.width.equalTo(self.currentPriceLabel);
        }];
        
        [self.positionRatioTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.right.equalTo(self.positionRatioLabel);
            make.centerY.equalTo(self.stockCodeLabel);
        }];
    }

    // 投资风格视图
    UIView *styleView = [[UIView alloc] init];
    {
        styleView.backgroundColor = UIColor.fm_F7F7F7_2E2F33;
        [self.mainStackView addArrangedSubview:styleView];
        [styleView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(15);
            make.right.equalTo(-15);
            make.height.equalTo(32);
        }];
        self.styleView = styleView;
        
        UILabel *styleValueLabel = [[UILabel alloc] init];
        styleValueLabel.font = FontWithSize(14);
        styleValueLabel.textColor = UIColor.up_textPrimaryColor;
        styleValueLabel.text = @"投资风格：";
        [styleView addSubview:styleValueLabel];
        [styleValueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(15);
            make.centerY.equalTo(0);
        }];
        self.styleValueLabel = styleValueLabel;
    }

    // 详情TableView
    self.detailTableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    self.detailTableView.delegate = self;
    self.detailTableView.dataSource = self;
    self.detailTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.detailTableView.scrollEnabled = NO;
    self.detailTableView.backgroundColor = UIColor.up_contentBgColor;
    [self.detailTableView registerCellClass:[FMGSYJChiefInvestmentHoldsOperationCell class]];
    [self.mainStackView addArrangedSubview:self.detailTableView];
    [self.detailTableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.right.equalTo(0);
    }];
    
    [self.contentView addSepLineWithBlock:^(MASConstraintMaker * _Nonnull make) {
        make.left.equalTo(15);
        make.right.equalTo(-15);
        make.bottom.equalTo(0);
        make.height.equalTo(0.5);
    }].backgroundColor = UIColor.fm_sepline_color;
}

- (void)setModel:(FMGSYJChiefInvestmentHoldsModel *)model {
    _model = model;

    // 设置数据
    self.stockNameLabel.text = model.stock.stockName;
    if ([model.stock.stockCode hasPrefix:@"sh"] || [model.stock.stockCode hasPrefix:@"sz"]) {
        self.stockCodeLabel.text = [model.stock.stockCode substringFromIndex:2];
    } else {
        self.stockCodeLabel.text = model.stock.stockCode;
    }
    self.currentPriceLabel.text = [NSString stringWithFormat:@"%.2f/%.2f", model.stock.price, model.stock.buyPrice];
    self.highLowPriceLabel.text = [NSString stringWithFormat:@"%.2f/%.2f", model.stock.highPrice, model.stock.lowPrice];
    self.positionRatioLabel.text = [NSString stringWithFormat:@"%@%%", model.stock.curHoldScale];
    

    // 设置展开/折叠状态
    if (model.isUnfolded) {
        self.foldButton.transform = CGAffineTransformMakeRotation(M_PI);
        self.detailTableView.hidden = NO;
        
        if (self.serverType == JCVIPTypeGSYJDZ && model.stock.investment.length) {
            NSMutableAttributedString *attrStr = [[NSMutableAttributedString alloc] initWithString:[NSString stringWithFormat:@"投资风格：%@", model.stock.investment ?: @""]];
            [attrStr yy_setColor:ColorWithHex(0xfc6c00) range:NSMakeRange(0, 5)];
            self.styleValueLabel.attributedText = attrStr;
            self.styleView.hidden = NO;
        } else {
            self.styleView.hidden = YES;
        }

        [self.detailTableView reloadData];
        [self updateDetailTableViewHeight];
    } else {
        self.foldButton.transform = CGAffineTransformIdentity;
        self.styleView.hidden = self.detailTableView.hidden = YES;

        [self.detailTableView mas_updateConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(0);
        }];
    }
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.model.operas.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    FMGSYJChiefInvestmentHoldsOperationCell *cell = [tableView reuseCellClass:[FMGSYJChiefInvestmentHoldsOperationCell class]];
    cell.cellWidth = UI_SCREEN_WIDTH;
    cell.dakas = self.dakas;
    if (indexPath.row < self.model.operas.count) {
        cell.model = self.model.operas[indexPath.row];
    }
    WEAKSELF
    cell.refreshBlock = ^(FMGSYJChiefInvestmentOperationModel * _Nonnull model) {
        if (__weakSelf.refreshBlock) {
            __weakSelf.refreshBlock(__weakSelf.model);
        }
    };

    return cell;
}

#pragma mark - UITableViewDelegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}

- (CGFloat)tableView:(UITableView *)tableView estimatedHeightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return 120;
}

#pragma mark - Actions
- (void)jumpToStockDetail {
    UPMarketCodeMatchInfo *matchInfo = [FMUPDataTool matchInfoWithSetCodeAndCode:self.model.stock.stockCode];
    if (matchInfo) {
        [UPRouterUtil goMarketStock:matchInfo.setCode code:matchInfo.code];
    }
}

- (void)foldButtonClicked {
    self.model.isUnfolded = !self.model.isUnfolded;

    if (self.refreshBlock) {
        self.refreshBlock(self.model);
    }
}

#pragma mark - Private Methods

- (void)updateDetailTableViewHeight {
    // 使用calculatedHeight方法获取精确高度
    CGFloat contentHeight = 0;
    
    for (NSInteger i = 0; i < self.model.operas.count; i++) {
        // 获取或初始化cell
        NSIndexPath *indexPath = [NSIndexPath indexPathForRow:i inSection:0];
        FMGSYJChiefInvestmentHoldsOperationCell *cell = (FMGSYJChiefInvestmentHoldsOperationCell *)[self tableView:self.detailTableView cellForRowAtIndexPath:indexPath];
        
        // 使用cell的计算高度方法获取准确高度
        CGFloat cellHeight = [cell calculatedHeight];
        contentHeight += cellHeight;
    }
    
    // 更新tableView高度约束
    [self.detailTableView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(contentHeight);
    }];
    
    // 通知父视图更新布局
    [self.contentView setNeedsLayout];
    [self.contentView layoutIfNeeded];
}

@end
