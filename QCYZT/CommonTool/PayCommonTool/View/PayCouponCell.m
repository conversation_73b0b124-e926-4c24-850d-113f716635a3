//
//  PayCouponCell.m
//  QCYZT
//
//  Created by Mr.文 on 2025-01-29.
//  Copyright © 2025年 sdcf. All rights reserved.
//

#import "PayCouponCell.h"
#import "FMCouponTableModel.h"

@interface PayCouponCell ()

@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIView *couponBgView;
@property (nonatomic, strong) UILabel *couponNameLB;
@property (nonatomic, strong) UILabel *couponValueLB;
@property (nonatomic, strong) UIImageView *couponArrowImgV;

@property (nonatomic, strong) FMCouponTableModel *selectedCoupon;
@property (nonatomic, strong) NSArray<FMCouponTableModel *> *availableCoupons;
@property (nonatomic, copy) CouponTapBlock tapBlock;

@end

@implementation PayCouponCell

- (instancetype)initWithStyle:(UITableViewCellStyle)style reuseIdentifier:(NSString *)reuseIdentifier {
    if (self = [super initWithStyle:style reuseIdentifier:reuseIdentifier]) {
        [self setupViews];
    }
    return self;
}

- (void)setupViews {
    self.backgroundColor = UIColor.up_contentBgColor;
    self.selectionStyle = UITableViewCellSelectionStyleNone;
    
    // 标题标签
    UILabel *titleLabel = [[UILabel alloc] initWithFrame:CGRectZero 
                                                    font:FontWithSize(16) 
                                               textColor:UIColor.up_textSecondaryColor 
                                         backgroundColor:FMClearColor 
                                           numberOfLines:1 
                                           textAlignment:NSTextAlignmentLeft];
    titleLabel.text = @"优惠券:";
    [self.contentView addSubview:titleLabel];
    [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(15);
        make.centerY.equalTo(0);
    }];
    self.titleLabel = titleLabel;
    
    // 优惠券背景视图
    [self.contentView addSubview:self.couponBgView];
    [self.couponBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(-15);
        make.centerY.equalTo(0);
        make.height.equalTo(33);
        make.left.greaterThanOrEqualTo(titleLabel.mas_right);
    }];
    
    // 分割线
    [FMHelper drawLineWithSuperView:self.contentView
                              Color:UIColor.fm_sepline_color
                              Frame:CGRectMake(0, 53.5, UI_SCREEN_WIDTH, 0.5)];
}

- (void)configureWithSelectedCoupon:(FMCouponTableModel *)selectedCoupon
                   availableCoupons:(NSArray<FMCouponTableModel *> *)availableCoupons
                           tapBlock:(CouponTapBlock)tapBlock {
    self.selectedCoupon = selectedCoupon;
    self.availableCoupons = availableCoupons;
    self.tapBlock = tapBlock;
    
    [self updateCouponDisplayState];
}

- (void)updateCouponDisplayState {
    if (self.selectedCoupon) {
        [self configureSelectedCouponDisplay];
    } else {
        [self configureUnselectedCouponDisplay];
    }
}

- (void)configureSelectedCouponDisplay {
    // 设置优惠券价值显示
    self.couponValueLB.text = [NSString stringWithFormat:@"-%.0f金币", self.selectedCoupon.value];
    
    // 设置优惠券名称显示（超过8个字符时截断）
    NSString *couponName = self.selectedCoupon.name;
    if (couponName.length > 8) {
        self.couponNameLB.text = [NSString stringWithFormat:@"%@...", [couponName substringToIndex:8]];
    } else {
        self.couponNameLB.text = couponName;
    }
    
    // 设置选中状态的UI样式
    self.couponNameLB.textColor = UIColor.up_textPrimaryColor;
    self.couponArrowImgV.image = [UIImage imageNamed:@"pay_arrow"];
    self.couponBgView.backgroundColor = FMClearColor;
    
    // 更新约束
    [self.couponBgView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@0);
    }];
}

- (void)configureUnselectedCouponDisplay {
    CGFloat maxAvailableCouponValue = [self getMaxAvailableCouponValue];
    
    // 清空优惠券相关信息
    self.couponValueLB.text = @"";
    
    if (maxAvailableCouponValue <= 0) {
        [self configureNoCouponAvailableState];
    } else {
        [self configureCouponAvailableState:maxAvailableCouponValue];
    }
}

- (void)configureNoCouponAvailableState {
    self.couponNameLB.text = @"请选择";
    self.couponNameLB.textColor = ColorWithHex(0xBFBFBF);
    self.couponArrowImgV.image = ImageWithName(@"userCenter_arrow");
    self.couponBgView.backgroundColor = FMClearColor;
    
    [self.couponBgView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@0);
    }];
}

- (void)configureCouponAvailableState:(CGFloat)maxValue {
    self.couponNameLB.text = [NSString stringWithFormat:@"未选卡券，最高抵扣%.0f金币", maxValue];
    self.couponNameLB.textColor = FMNavColor;
    self.couponArrowImgV.image = [UIImage imageWithTintColor:FMNavColor 
                                                   blendMode:kCGBlendModeDestinationIn 
                                             WithImageObject:ImageWithName(@"userCenter_arrow")];
    self.couponBgView.backgroundColor = ColorWithHex(0xffebeb);
    
    [self.couponBgView mas_updateConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(@(-15));
    }];
}

- (CGFloat)getMaxAvailableCouponValue {
    CGFloat maxValue = -1;
    for (FMCouponTableModel *model in self.availableCoupons) {
        if (model.isEnable.boolValue && model.value > maxValue) {
            maxValue = model.value;
        }
    }
    return maxValue;
}

- (CGRect)couponBgViewFrame {
    return self.couponBgView.frame;
}

#pragma mark - Lazy Loading

- (UIView *)couponBgView {
    if (!_couponBgView) {
        _couponBgView = [UIView new];
        _couponBgView.backgroundColor = ColorWithHex(0xffebeb);
        UI_View_Radius(_couponBgView, 16.5);
        
        [_couponBgView addSubview:self.couponArrowImgV];
        [self.couponArrowImgV mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(0);
            make.right.equalTo(-15);
        }];
        
        [_couponBgView addSubview:self.couponValueLB];
        [self.couponValueLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerY.equalTo(0);
            make.right.equalTo(self.couponArrowImgV.mas_left).offset(-5);
        }];
        
        [_couponBgView addSubview:self.couponNameLB];
        [self.couponNameLB mas_makeConstraints:^(MASConstraintMaker *make) {
            make.right.equalTo(self.couponValueLB.mas_left).offset(0);
            make.centerY.equalTo(0);
            make.left.equalTo(15);
        }];
        
        _couponBgView.userInteractionEnabled = YES;
        WEAKSELF;
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithActionBlock:^(id  _Nonnull sender) {
            if (__weakSelf.tapBlock) {
                __weakSelf.tapBlock();
            }
        }];
        [_couponBgView addGestureRecognizer:tap];
    }
    return _couponBgView;
}

- (UILabel *)couponNameLB {
    if (!_couponNameLB) {
        _couponNameLB = [[UILabel alloc]init];
        _couponNameLB.text = @"请选择";
        _couponNameLB.textAlignment = NSTextAlignmentRight;
        _couponNameLB.font = [UIFont systemFontOfSize:16];
        _couponNameLB.textColor = UIColor.fm_BFBFBF_888888;
    }
    return _couponNameLB;
}

- (UILabel *)couponValueLB {
    if (!_couponValueLB) {
        _couponValueLB = [[UILabel alloc] initWithFrame:CGRectZero 
                                                   font:FontWithSize(16) 
                                              textColor:FMNavColor 
                                        backgroundColor:FMClearColor 
                                          numberOfLines:1 
                                          textAlignment:NSTextAlignmentRight];
    }
    return _couponValueLB;
}

- (UIImageView *)couponArrowImgV {
    if (!_couponArrowImgV) {
        _couponArrowImgV = [[UIImageView alloc] initWithImage:ImageWithName(@"userCenter_arrow")];
    }
    return _couponArrowImgV;
}

@end
